"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Citizen } from "@/lib/api/reports";
import { Plus, Trash2, Users } from "lucide-react";
import { useState } from "react";

interface AttendanceSelectorProps {
  isiboMembers: Citizen[];
  selectedAttendees: Citizen[];
  onAttendanceChange: (attendees: Citizen[]) => void;
}

export function AttendanceSelector({
  isiboMembers,
  selectedAttendees,
  onAttendanceChange,
}: AttendanceSelectorProps) {
  const [newAttendee, setNewAttendee] = useState({
    names: "",
    email: "",
    phone: "",
  });

  const handleMemberToggle = (member: Citizen, checked: boolean) => {
    if (checked) {
      // Add member to selected attendees
      const updatedAttendees = [...selectedAttende<PERSON>, member];
      onAttendanceChange(updatedAttendees);
    } else {
      // Remove member from selected attendees
      const updatedAttendees = selectedAttendees.filter(
        (attendee) =>
          attendee.names !== member.names ||
          attendee.email !== member.email ||
          attendee.phone !== member.phone
      );
      onAttendanceChange(updatedAttendees);
    }
  };

  const isMemberSelected = (member: Citizen) => {
    return selectedAttendees.some(
      (attendee) =>
        attendee.names === member.names &&
        attendee.email === member.email &&
        attendee.phone === member.phone
    );
  };

  const handleAddNewAttendee = () => {
    if (newAttendee.names.trim() && newAttendee.email.trim() && newAttendee.phone.trim()) {
      // Check if attendee already exists
      const exists = selectedAttendees.some(
        (attendee) =>
          attendee.names === newAttendee.names ||
          attendee.email === newAttendee.email ||
          attendee.phone === newAttendee.phone
      );

      if (!exists) {
        const updatedAttendees = [...selectedAttendees, { ...newAttendee }];
        onAttendanceChange(updatedAttendees);
        setNewAttendee({ names: "", email: "", phone: "" });
      }
    }
  };

  const handleRemoveAttendee = (attendeeToRemove: Citizen) => {
    const updatedAttendees = selectedAttendees.filter(
      (attendee) =>
        attendee.names !== attendeeToRemove.names ||
        attendee.email !== attendeeToRemove.email ||
        attendee.phone !== attendeeToRemove.phone
    );
    onAttendanceChange(updatedAttendees);
  };

  const isNewAttendeeFromIsibo = (attendee: Citizen) => {
    return isiboMembers.some(
      (member) =>
        member.names === attendee.names &&
        member.email === attendee.email &&
        member.phone === attendee.phone
    );
  };

  return (
    <div className="space-y-6">
      {/* Isibo Members Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Isibo Members
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isiboMembers.length > 0 ? (
            <div className="space-y-3">
              {isiboMembers.map((member, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <Checkbox
                    id={`member-${index}`}
                    checked={isMemberSelected(member)}
                    onCheckedChange={(checked) =>
                      handleMemberToggle(member, checked as boolean)
                    }
                  />
                  <Label
                    htmlFor={`member-${index}`}
                    className="flex-1 cursor-pointer"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">{member.names}</span>
                      <span className="text-sm text-muted-foreground">
                        {member.email} • {member.phone}
                      </span>
                    </div>
                  </Label>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground">No members found in this isibo.</p>
          )}
        </CardContent>
      </Card>

      {/* Add Additional Attendees */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add Additional Attendees
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="names">Full Names</Label>
              <Input
                id="names"
                value={newAttendee.names}
                onChange={(e) =>
                  setNewAttendee((prev) => ({ ...prev, names: e.target.value }))
                }
                placeholder="Enter full names"
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={newAttendee.email}
                onChange={(e) =>
                  setNewAttendee((prev) => ({ ...prev, email: e.target.value }))
                }
                placeholder="Enter email"
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={newAttendee.phone}
                onChange={(e) =>
                  setNewAttendee((prev) => ({ ...prev, phone: e.target.value }))
                }
                placeholder="Enter phone number"
              />
            </div>
          </div>
          <div className="mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleAddNewAttendee}
              disabled={
                !newAttendee.names.trim() ||
                !newAttendee.email.trim() ||
                !newAttendee.phone.trim()
              }
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Attendee
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Selected Attendees Summary */}
      {selectedAttendees.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Selected Attendees ({selectedAttendees.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {selectedAttendees.map((attendee, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-muted rounded-lg"
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{attendee.names}</span>
                    <span className="text-sm text-muted-foreground">
                      {attendee.email} • {attendee.phone}
                    </span>
                    {isNewAttendeeFromIsibo(attendee) && (
                      <span className="text-xs text-blue-600">Isibo Member</span>
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveAttendee(attendee)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
