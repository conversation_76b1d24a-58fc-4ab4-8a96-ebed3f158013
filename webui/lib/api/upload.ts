import * as Minio from 'minio';
import { MINIO_CONFIG, MINIO_URL } from '../config/minio';

export interface UploadResponse {
  url: string;
  filename: string;
  size: number;
  mimetype: string;
}

// Create MinIO client
const minioClient = new Minio.Client({
  endPoint: MINIO_CONFIG.endPoint,
  port: MINIO_CONFIG.port,
  useSSL: MINIO_CONFIG.useSSL,
  accessKey: MINIO_CONFIG.accessKey,
  secretKey: MINIO_CONFIG.secretKey,
});

/**
 * Initialize MinIO bucket if it doesn't exist
 */
async function initializeBucket(): Promise<void> {
  try {
    const bucketExists = await minioClient.bucketExists(MINIO_CONFIG.bucketName);
    if (!bucketExists) {
      await minioClient.makeBucket(MINIO_CONFIG.bucketName, MINIO_CONFIG.region);
      console.log(`Bucket ${MINIO_CONFIG.bucketName} created successfully`);
    }
  } catch (error) {
    console.error('Error initializing bucket:', error);
    // Don't throw error here as bucket might already exist
  }
}

/**
 * Generate a unique filename with timestamp and random string
 * @param originalName Original filename
 * @returns Unique filename
 */
function generateUniqueFilename(originalName: string): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = originalName.split('.').pop();
  const nameWithoutExtension = originalName.replace(/\.[^/.]+$/, "");
  const sanitizedName = nameWithoutExtension.replace(/[^a-zA-Z0-9]/g, '_');

  return `${timestamp}_${randomString}_${sanitizedName}.${extension}`;
}

/**
 * Upload a file to MinIO bucket
 * @param file File to upload
 * @returns Promise with upload response containing the file URL
 */
export async function uploadFile(file: File): Promise<UploadResponse> {
  try {
    // Initialize bucket if it doesn't exist
    await initializeBucket();

    // Generate unique filename
    const filename = generateUniqueFilename(file.name);

    // Convert File to Buffer for MinIO
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload file to MinIO
    await minioClient.putObject(
      MINIO_CONFIG.bucketName,
      filename,
      buffer,
      file.size,
      {
        'Content-Type': file.type,
        'Content-Disposition': `inline; filename="${file.name}"`,
      }
    );

    // Generate the public URL
    const url = `${MINIO_URL}/${MINIO_CONFIG.bucketName}/${filename}`;

    return {
      url,
      filename,
      size: file.size,
      mimetype: file.type,
    };
  } catch (error) {
    console.error("Upload file error:", error);
    throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload multiple files to MinIO bucket
 * @param files Array of files to upload
 * @returns Promise with array of upload responses
 */
export async function uploadFiles(files: File[]): Promise<UploadResponse[]> {
  try {
    const uploadPromises = files.map(file => uploadFile(file));
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error("Upload files error:", error);
    throw error;
  }
}

/**
 * Get file extension from filename
 * @param filename The filename
 * @returns File extension
 */
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}

/**
 * Check if file type is allowed
 * @param file File to check
 * @param allowedTypes Array of allowed MIME types
 * @returns Boolean indicating if file type is allowed
 */
export function isFileTypeAllowed(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type);
}

/**
 * Format file size in human readable format
 * @param bytes File size in bytes
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Validate file size
 * @param file File to validate
 * @param maxSizeInMB Maximum file size in MB
 * @returns Boolean indicating if file size is valid
 */
export function isFileSizeValid(file: File, maxSizeInMB: number): boolean {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return file.size <= maxSizeInBytes;
}

/**
 * Delete a file from MinIO bucket
 * @param fileUrl The full URL of the file to delete
 * @returns Promise indicating success
 */
export async function deleteFile(fileUrl: string): Promise<void> {
  try {
    // Extract filename from URL
    const filename = fileUrl.split('/').pop();
    if (!filename) {
      throw new Error('Invalid file URL');
    }

    await minioClient.removeObject(MINIO_CONFIG.bucketName, filename);
  } catch (error) {
    console.error("Delete file error:", error);
    throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate a presigned URL for downloading a file
 * @param fileUrl The full URL of the file
 * @param expiryInSeconds Expiry time in seconds (default: 7 days)
 * @returns Promise with presigned URL
 */
export async function getPresignedDownloadUrl(fileUrl: string, expiryInSeconds: number = 7 * 24 * 60 * 60): Promise<string> {
  try {
    // Extract filename from URL
    const filename = fileUrl.split('/').pop();
    if (!filename) {
      throw new Error('Invalid file URL');
    }

    return await minioClient.presignedGetObject(MINIO_CONFIG.bucketName, filename, expiryInSeconds);
  } catch (error) {
    console.error("Get presigned URL error:", error);
    throw new Error(`Failed to generate download URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Check if a file exists in the bucket
 * @param fileUrl The full URL of the file
 * @returns Promise indicating if file exists
 */
export async function fileExists(fileUrl: string): Promise<boolean> {
  try {
    // Extract filename from URL
    const filename = fileUrl.split('/').pop();
    if (!filename) {
      return false;
    }

    await minioClient.statObject(MINIO_CONFIG.bucketName, filename);
    return true;
  } catch (error) {
    return false;
  }
}
