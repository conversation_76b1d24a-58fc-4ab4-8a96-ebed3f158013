import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { plainToInstance } from "class-transformer";
import { paginate } from "nestjs-typeorm-paginate";
import { Brackets, Repository } from "typeorm";
import { Isibo } from "../locations/entities/isibo.entity";
import { IsibosService } from "../locations/isibos.service";
import { VillagesService } from "../locations/villages.service";
import { CreateActivityDTO } from "./dto/create-activity.dto";
import { FetchActivityDTO } from "./dto/fetch-activity.dto";
import { UpdateActivityDTO } from "./dto/update-activity.dto";
import { Activity } from "./entities/activity.entity";
import { Task } from "./entities/task.entity";
import { EActivityStatus } from "./enum/EActivityStatus";
import { ETaskStatus } from "./enum/ETaskStatus";

@Injectable()
export class ActivitiesService {
  constructor(
    @InjectRepository(Activity)
    private readonly activityRepository: Repository<Activity>,
    private readonly isibosService: IsibosService,
    private readonly villagesService: VillagesService,
  ) {}

  async create(
    createActivityDTO: CreateActivityDTO.Input,
  ): Promise<CreateActivityDTO.Output> {
    let village = null;

    if (createActivityDTO.villageId) {
      village = await this.villagesService.findVillageById(
        createActivityDTO.villageId,
      );

      if (!village) {
        throw new NotFoundException("Village not found");
      }
    }

    const activity = this.activityRepository.create({
      title: createActivityDTO.title,
      description: createActivityDTO.description,
      date: new Date(createActivityDTO.date),
      status: EActivityStatus.PENDING,
      village,
    });

    if (createActivityDTO.tasks && createActivityDTO.tasks.length > 0) {
      activity.tasks = [];

      // Map to track isibo IDs already assigned to tasks for this activity
      const assignedIsibos = new Set<string>();

      for (const taskDto of createActivityDTO.tasks) {
        // Check if isibo is provided and required
        if (!taskDto.isiboId) {
          throw new BadRequestException("Isibo ID is required for each task");
        }

        // Check if isibo is already assigned to another task in this activity
        if (assignedIsibos.has(taskDto.isiboId)) {
          throw new ConflictException(
            `Isibo with ID ${taskDto.isiboId} is already assigned to another task in this activity`,
          );
        }

        const task = new Task();
        task.title = taskDto.title;
        task.description = taskDto.description;
        task.status = ETaskStatus.PENDING;
        task.activity = activity; // Set the activity reference

        // Find isibo
        const isibo = await this.isibosService.findIsiboById(taskDto.isiboId);
        if (!isibo) {
          throw new NotFoundException(
            `Isibo with ID ${taskDto.isiboId} not found`,
          );
        }
        task.isibo = isibo;
        assignedIsibos.add(taskDto.isiboId);

        activity.tasks.push(task);
      }
    }

    const savedActivity = await this.activityRepository.save(activity);

    // Create a clean object without circular references
    const activityData = {
      id: savedActivity.id,
      title: savedActivity.title,
      description: savedActivity.description,
      date: savedActivity.date,
      status: savedActivity.status,
      tasks: savedActivity.tasks
        ? savedActivity.tasks.map((task) => ({
            id: task.id,
            title: task.title,
            description: task.description,
            status: task.status,
          }))
        : [],
    };

    return plainToInstance(CreateActivityDTO.Output, activityData);
  }

  async delete(id: string): Promise<void> {
    try {
      const activity = await this.activityRepository.findOne({
        where: { id },
      });

      if (!activity) {
        throw new NotFoundException("Activity not found");
      }

      await this.activityRepository.softDelete(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Activity deletion failed: ${error.message}`,
      );
    }
  }

  async findOne(id: string): Promise<Activity> {
    const activity = await this.activityRepository.findOne({
      where: { id },
      relations: ["village", "tasks", "tasks.isibo"],
    });

    if (!activity) {
      throw new NotFoundException("Activity not found");
    }

    return activity;
  }

  async findAll(DTO: FetchActivityDTO.Input) {
    const queryBuilder = this.activityRepository
      .createQueryBuilder("activity")
      .leftJoinAndSelect("activity.village", "village")
      .leftJoinAndSelect("activity.tasks", "tasks")
      .leftJoinAndSelect("tasks.isibo", "isibo")
      .orderBy("activity.createdAt", "DESC");

    if (DTO.q) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where("activity.title ILIKE :searchKey", {
            searchKey: `%${DTO.q}%`,
          }).orWhere("activity.description ILIKE :searchKey", {
            searchKey: `%${DTO.q}%`,
          });
        }),
      );
    }

    if (DTO.status) {
      queryBuilder.andWhere("activity.status = :status", {
        status: DTO.status,
      });
    }

    if (DTO.villageId) {
      queryBuilder.andWhere("village.id = :villageId", {
        villageId: DTO.villageId,
      });
    }

    const paginatedResult = await paginate<Activity>(queryBuilder, {
      page: DTO.page,
      limit: DTO.size,
    });

    return {
      ...paginatedResult,
      items: paginatedResult.items.map((activity) => {
        // Create a clean object without circular references
        const activityData = {
          id: activity.id,
          title: activity.title,
          description: activity.description,
          date: activity.date,
          status: activity.status,
          village: activity.village
            ? {
                id: activity.village.id,
                name: activity.village.name,
              }
            : undefined,
          tasks: activity.tasks
            ? activity.tasks.map((task) => ({
                id: task.id,
                title: task.title,
                description: task.description,
                status: task.status,
                isibo: task.isibo
                  ? {
                      id: task.isibo.id,
                      name: task.isibo.name,
                    }
                  : undefined,
              }))
            : [],
        };

        return plainToInstance(FetchActivityDTO.Output, activityData);
      }),
    };
  }

  async getActivityDetails(id: string) {
    const activity = await this.findOne(id);

    // Create a clean object without circular references
    const activityData = {
      id: activity.id,
      title: activity.title,
      description: activity.description,
      date: activity.date,
      status: activity.status,
      village: activity.village
        ? {
            id: activity.village.id,
            name: activity.village.name,
          }
        : undefined,
      tasks: activity.tasks
        ? activity.tasks.map((task) => ({
            id: task.id,
            title: task.title,
            description: task.description,
            status: task.status,
            isibo: task.isibo
              ? {
                  id: task.isibo.id,
                  name: task.isibo.name,
                }
              : undefined,
          }))
        : [],
    };

    return plainToInstance(FetchActivityDTO.Output, activityData);
  }

  async update(
    id: string,
    updateActivityDTO: UpdateActivityDTO.Input,
  ): Promise<UpdateActivityDTO.Output> {
    const activity = await this.activityRepository.findOne({
      where: { id },
      relations: ["village", "tasks", "tasks.isibo"],
    });

    if (!activity) {
      throw new NotFoundException("Activity not found");
    }

    let village = null;

    if (updateActivityDTO.villageId) {
      village = await this.villagesService.findVillageById(
        updateActivityDTO.villageId,
      );

      if (!village) {
        throw new NotFoundException("Village not found");
      }

      activity.village = village;
    }

    // Update basic fields
    if (updateActivityDTO.title) {
      activity.title = updateActivityDTO.title;
    }

    if (updateActivityDTO.description) {
      activity.description = updateActivityDTO.description;
    }

    if (updateActivityDTO.date) {
      activity.date = new Date(updateActivityDTO.date);
    }

    if (updateActivityDTO.status) {
      activity.status = updateActivityDTO.status;
    }

    // Handle tasks if provided
    if (updateActivityDTO.tasks) {
      if (!activity.tasks) {
        activity.tasks = [];
      }

      // Keep track of task IDs that should be kept
      const taskIdsToKeep = updateActivityDTO.tasks
        .filter((task) => task.id)
        .map((task) => task.id);

      // Remove tasks that are not in the update DTO
      activity.tasks = activity.tasks.filter((task) =>
        taskIdsToKeep.includes(task.id),
      );

      // Map to track isibo IDs already assigned to tasks for this activity
      const assignedIsibos = new Map<string, string>(); // Maps isiboId -> taskId

      // First, add all existing tasks to the tracking map
      for (const task of activity.tasks) {
        if (task.isibo) {
          assignedIsibos.set(task.isibo.id, task.id);
        }
      }

      // Process each task in the DTO
      for (const taskDto of updateActivityDTO.tasks) {
        if (taskDto.id) {
          // Update existing task
          const existingTask = activity.tasks.find(
            (task) => task.id === taskDto.id,
          );

          if (existingTask) {
            // Update task fields
            if (taskDto.title) {
              existingTask.title = taskDto.title;
            }

            if (taskDto.description) {
              existingTask.description = taskDto.description;
            }

            if (taskDto.status) {
              existingTask.status = taskDto.status;
            }

            // Store the original isibo ID before any changes
            const originalIsiboId = existingTask.isibo?.id;

            // Update isibo if provided
            if (taskDto.isiboId) {
              // If the isibo ID is different from the current one
              if (
                !existingTask.isibo ||
                taskDto.isiboId !== existingTask.isibo.id
              ) {
                // Check if this isibo is already assigned to another task
                const existingTaskId = assignedIsibos.get(taskDto.isiboId);
                if (existingTaskId && existingTaskId !== existingTask.id) {
                  throw new ConflictException(
                    `Isibo with ID ${taskDto.isiboId} is already assigned to another task in this activity`,
                  );
                }

                const isibo = await this.isibosService.findIsiboById(
                  taskDto.isiboId,
                );

                if (!isibo) {
                  throw new NotFoundException(
                    `Isibo with ID ${taskDto.isiboId} not found`,
                  );
                }

                // If this task had a previous isibo, remove it from the tracking map
                if (existingTask.isibo) {
                  assignedIsibos.delete(existingTask.isibo.id);
                }

                // Create a new isibo reference to avoid potential issues with entity references
                existingTask.isibo = { id: isibo.id } as Isibo;
                assignedIsibos.set(isibo.id, existingTask.id);
              }
            }
          }
        } else {
          // Create new task
          const newTask = new Task();
          newTask.title = taskDto.title;
          newTask.description = taskDto.description;
          newTask.status = ETaskStatus.PENDING;

          // Use a simple reference to avoid circular references
          newTask.activity = { id: activity.id } as Activity;

          // Find isibo - required for new tasks
          if (!taskDto.isiboId) {
            throw new BadRequestException("Isibo ID is required for each task");
          }

          // Check if this isibo is already assigned to another task
          const existingTaskId = assignedIsibos.get(taskDto.isiboId);
          if (existingTaskId) {
            throw new ConflictException(
              `Isibo with ID ${taskDto.isiboId} is already assigned to another task in this activity`,
            );
          }

          const isibo = await this.isibosService.findIsiboById(taskDto.isiboId);

          if (!isibo) {
            throw new NotFoundException(
              `Isibo with ID ${taskDto.isiboId} not found`,
            );
          }

          // Use a simple reference to avoid circular references
          newTask.isibo = { id: isibo.id } as Isibo;
          assignedIsibos.set(taskDto.isiboId, "new-task"); // Mark as assigned

          // Add to activity tasks
          activity.tasks.push(newTask);
        }
      }
    }

    // Save the activity with its tasks
    const savedActivity = await this.activityRepository.save(activity);

    // Create a clean object without circular references
    const activityData = {
      id: savedActivity.id,
      title: savedActivity.title,
      description: savedActivity.description,
      date: savedActivity.date,
      status: savedActivity.status,
      village: savedActivity.village
        ? {
            id: savedActivity.village.id,
            name: savedActivity.village.name,
          }
        : undefined,
      tasks: savedActivity.tasks
        ? savedActivity.tasks.map((task) => ({
            id: task.id,
            title: task.title,
            description: task.description,
            status: task.status,
            isibo: task.isibo
              ? {
                  id: task.isibo.id,
                  name: task.isibo.name,
                }
              : undefined,
          }))
        : [],
    };

    return plainToInstance(UpdateActivityDTO.Output, activityData);
  }
}
