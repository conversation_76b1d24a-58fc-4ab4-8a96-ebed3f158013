import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, MinLength } from 'class-validator';

export namespace PasswordResetDto {
  export class RequestInput {
    @ApiProperty({
      description: 'Email address of the user requesting password reset',
      example: '<EMAIL>',
    })
    @IsEmail()
    @IsNotEmpty()
    email: string;
  }

  export class ResetInput {
    @ApiProperty({
      description: 'Password reset token',
      example: 'abc123def456ghi789',
    })
    @IsString()
    @IsNotEmpty()
    token: string;

    @ApiProperty({
      description: 'New password (minimum 8 characters)',
      example: 'NewSecurePassword123!',
      minLength: 8,
    })
    @IsString()
    @MinLength(8, { message: 'Password must be at least 8 characters long' })
    @IsNotEmpty()
    newPassword: string;
  }
}
